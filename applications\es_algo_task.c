/**
 * @file es_algo_task.c
 * @brief Vehicle Algorithm Task Implementation
 * <AUTHOR> Framework
 * @date 2025-07-28
 * @version 1.0
 */

#include "es_algo.h"
#include "es_log.h"
#include "es_drv_os.h"
#include "ev_hc32f460_lqfp100_v2_lsm6dsl.h"
#include "es_mdm.h"

#define TAG "ALGO_TASK"

/* Task context */
typedef struct {
    es_coro_task_t sensor_task;
    es_coro_task_t gps_task;
    es_coro_task_t stats_task;
    bool initialized;
} es_algo_task_ctx_t;

static es_algo_task_ctx_t s_task_ctx;

/* Task function declarations */
static es_async_t es_algo_sensor_task_func(es_coro_t *coro, void *ctx);
static es_async_t es_algo_gps_task_func(es_coro_t *coro, void *ctx);
static es_async_t es_algo_stats_task_func(es_coro_t *coro, void *ctx);

/**
 * @brief Initialize algorithm tasks
 */
int es_algo_task_init(void)
{
    if (s_task_ctx.initialized) {
        return 0;
    }

    /* Initialize algorithm module first */
    int ret = es_algo_init();
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize algorithm module: %d", ret);
        return ret;
    }

    /* Initialize sensor task */
    s_task_ctx.sensor_task = (es_coro_task_t) {
        .name = "algo_sensor",
        .func = es_algo_sensor_task_func,
        .ctx = NULL
    };

    /* Initialize GPS task */
    s_task_ctx.gps_task = (es_coro_task_t) {
        .name = "algo_gps", 
        .func = es_algo_gps_task_func,
        .ctx = NULL
    };

    /* Initialize statistics task */
    s_task_ctx.stats_task = (es_coro_task_t) {
        .name = "algo_stats",
        .func = es_algo_stats_task_func,
        .ctx = NULL
    };

    /* Add tasks to scheduler */
    es_scheduler_t *scheduler = es_scheduler_get_default();
    
    ret = es_scheduler_task_add(scheduler, &s_task_ctx.sensor_task);
    if (ret < 0) {
        ES_PRINTF_I(TAG, "Failed to add sensor task: %d", ret);
        return ret;
    }

    ret = es_scheduler_task_add(scheduler, &s_task_ctx.gps_task);
    if (ret < 0) {
        ES_PRINTF_I(TAG, "Failed to add GPS task: %d", ret);
        return ret;
    }

    ret = es_scheduler_task_add(scheduler, &s_task_ctx.stats_task);
    if (ret < 0) {
        ES_PRINTF_I(TAG, "Failed to add stats task: %d", ret);
        return ret;
    }

    s_task_ctx.initialized = true;
    ES_PRINTF_I(TAG, "Algorithm tasks initialized successfully");
    return 0;
}

/**
 * @brief Deinitialize algorithm tasks
 */
void es_algo_task_deinit(void)
{
    if (!s_task_ctx.initialized) {
        return;
    }

    /* Remove tasks from scheduler */
    es_scheduler_t *scheduler = es_scheduler_get_default();
    es_scheduler_task_remove(scheduler, &s_task_ctx.sensor_task);
    es_scheduler_task_remove(scheduler, &s_task_ctx.gps_task);
    es_scheduler_task_remove(scheduler, &s_task_ctx.stats_task);

    /* Deinitialize algorithm module */
    es_algo_deinit();

    s_task_ctx.initialized = false;
    ES_PRINTF_I(TAG, "Algorithm tasks deinitialized");
}

/**
 * @brief Sensor data collection task
 */
static es_async_t es_algo_sensor_task_func(es_coro_t *coro, void *ctx)
{
    static stc_lsm6dsl_axis_t accel_data;
    static stc_lsm6dsl_axis_t gyro_data;
    static float temperature;
    static uint32_t last_sample_time = 0;
    static uint32_t sample_interval_ms = 1000 / ES_ALGO_SENSOR_SAMPLE_RATE_HZ; /* 20ms for 50Hz */
    int err;

    es_co_begin(coro);

    while (1) {
        /* Wait for sample interval */
        uint32_t current_time = es_drv_os_get_tick_ms();
        if (current_time - last_sample_time < sample_interval_ms) {
            es_co_yield;
            continue;
        }

        /* Read accelerometer data */
        es_co_await_ex(err, BSP_LSM6DSL_Co_ReadAccel, &accel_data);
        if (err != 0) {
            ES_PRINTF_I(TAG, "Failed to read accelerometer: %d", err);
            es_co_yield;
            continue;
        }

        /* Read gyroscope data */
        es_co_await_ex(err, BSP_LSM6DSL_Co_ReadGyro, &gyro_data);
        if (err != 0) {
            ES_PRINTF_I(TAG, "Failed to read gyroscope: %d", err);
            es_co_yield;
            continue;
        }

        /* Read temperature data */
        es_co_await_ex(err, BSP_LSM6DSL_Co_ReadTemp, &temperature);
        if (err != 0) {
            ES_PRINTF_I(TAG, "Failed to read temperature: %d", err);
            temperature = 25.0f; /* Default temperature */
        }

        /* Process sensor data */
        int ret = es_algo_process_sensor_data(&accel_data, &gyro_data, temperature);
        if (ret != 0) {
            ES_PRINTF_I(TAG, "Failed to process sensor data: %d", ret);
        }

        last_sample_time = current_time;
        es_co_yield;
    }

    es_co_end;
}

/**
 * @brief GPS data collection task
 */
static es_async_t es_algo_gps_task_func(es_coro_t *coro, void *ctx)
{
    static uint32_t last_gps_time = 0;
    static uint32_t gps_interval_ms = 1000 / ES_ALGO_GPS_SAMPLE_RATE_HZ; /* 1000ms for 1Hz */

    es_co_begin(coro);

    while (1) {
        /* Wait for GPS interval */
        uint32_t current_time = es_drv_os_get_tick_ms();
        if (current_time - last_gps_time < gps_interval_ms) {
            es_co_yield;
            continue;
        }

        /* Get GPS data from modem module */
        const gps_t *gps_data = es_mdm_get_gps_data();
        if (gps_data != NULL) {
            int ret = es_algo_process_gps_data(gps_data);
            if (ret != 0) {
                ES_PRINTF_I(TAG, "Failed to process GPS data: %d", ret);
            }
        } else {
            ES_PRINTF_I(TAG, "No GPS data available");
        }

        last_gps_time = current_time;
        es_co_yield;
    }

    es_co_end;
}

/**
 * @brief Statistics reporting task
 */
static es_async_t es_algo_stats_task_func(es_coro_t *coro, void *ctx)
{
    static uint32_t last_stats_time = 0;
    static uint32_t stats_interval_ms = 30000; /* 30 seconds */

    es_co_begin(coro);

    while (1) {
        /* Wait for stats interval */
        uint32_t current_time = es_drv_os_get_tick_ms();
        if (current_time - last_stats_time < stats_interval_ms) {
            es_co_yield;
            continue;
        }

        /* Get and report statistics */
        uint32_t sensor_samples, gps_samples, invalid_sensor, invalid_gps;
        es_algo_get_statistics(&sensor_samples, &gps_samples, &invalid_sensor, &invalid_gps);

        ES_PRINTF_I(TAG, "Stats: sensor=%lu, gps=%lu, invalid_sensor=%lu, invalid_gps=%lu", 
                   sensor_samples, gps_samples, invalid_sensor, invalid_gps);

        /* Get algorithm context for debugging */
        const es_algo_context_t *ctx = es_algo_get_context();
        if (ctx->orientation.calibrated) {
            ES_PRINTF_I(TAG, "Orientation: roll=%.1f°, pitch=%.1f°, yaw=%.1f°", 
                       ctx->orientation.roll_deg, ctx->orientation.pitch_deg, ctx->orientation.yaw_deg);
        } else {
            ES_PRINTF_I(TAG, "Orientation: not calibrated");
        }

        last_stats_time = current_time;
        es_co_yield;
    }

    es_co_end;
}
