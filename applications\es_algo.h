/**
 * @file es_algo.h
 * @brief Vehicle Event Detection Algorithm Module
 * <AUTHOR> Framework
 * @date 2025-07-28
 * @version 1.0
 * 
 * This module implements vehicle event detection algorithms by fusing
 * LSM6DSL sensor data (accelerometer and gyroscope) with GPS data.
 * It can detect rapid acceleration, deceleration, sharp turns, and
 * vehicle tipping events. The module handles variable device mounting
 * orientations and includes data filtering algorithms.
 */

#ifndef __ES_ALGO_H__
#define __ES_ALGO_H__

#include <stdint.h>
#include <stdbool.h>
#include <math.h>
#include "es_coro.h"
#include "es_scheduler.h"
#include "es_frame.h"
#include "ev_hc32f460_lqfp100_v2_lsm6dsl.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Algorithm configuration constants */
#define ES_ALGO_SENSOR_SAMPLE_RATE_HZ       50      /* LSM6DSL sampling rate */
#define ES_ALGO_GPS_SAMPLE_RATE_HZ          1       /* GPS sampling rate */
#define ES_ALGO_FILTER_WINDOW_SIZE          10      /* Moving average window */
#define ES_ALGO_CALIBRATION_SAMPLES         100     /* Samples for orientation calibration */

/* Event detection thresholds (considering LSM6DSL data format) */
#define ES_ALGO_ACCEL_THRESHOLD_MG          300     /* Rapid acceleration threshold (mg) */
#define ES_ALGO_DECEL_THRESHOLD_MG          400     /* Rapid deceleration threshold (mg) */
#define ES_ALGO_TURN_THRESHOLD_MDPS         30000   /* Sharp turn threshold (mdps) */
#define ES_ALGO_TILT_THRESHOLD_DEG          45.0f   /* Tipping threshold (degrees) */
#define ES_ALGO_SPEED_THRESHOLD_KMH         5       /* Minimum speed for event detection */

/* Data validation thresholds (considering LSM6DSL data format) */
#define ES_ALGO_MAX_ACCEL_MG                8000    /* Maximum valid acceleration (mg) */
#define ES_ALGO_MAX_GYRO_MDPS               200000  /* Maximum valid gyro rate (mdps) */
#define ES_ALGO_GPS_MIN_SATELLITES          4       /* Minimum GPS satellites */
#define ES_ALGO_GPS_MAX_HDOP                500     /* Maximum GPS HDOP (x100) */

/* Fixed-point scaling factors */
#define ES_ALGO_FIXED_SCALE                 1000    /* Q10.3 fixed point scale */
#define ES_ALGO_ANGLE_SCALE                 100     /* Angle scale (0.01 degree precision) */
#define ES_ALGO_ACCEL_SCALE                 1       /* mg scale (already in mg) */
#define ES_ALGO_GYRO_SCALE                  1       /* mdps scale (already in mdps) */

/* Fixed-point conversion macros */
#define ES_ALGO_MG_TO_FIXED(mg)             ((int32_t)(mg))
#define ES_ALGO_MDPS_TO_FIXED(mdps)         ((int32_t)(mdps))
#define ES_ALGO_FIXED_TO_MG(fixed)          ((int16_t)(fixed))
#define ES_ALGO_FIXED_TO_MDPS(fixed)        ((int32_t)(fixed))
#define ES_ALGO_ANGLE_TO_FIXED(deg)         ((int16_t)((deg) * ES_ALGO_ANGLE_SCALE))
#define ES_ALGO_FIXED_TO_ANGLE(fixed)       ((int16_t)((fixed) / ES_ALGO_ANGLE_SCALE))

/**
 * @brief Vehicle event types
 */
typedef enum {
    ES_ALGO_EVENT_NONE = 0,
    ES_ALGO_EVENT_RAPID_ACCEL,      /* Rapid acceleration */
    ES_ALGO_EVENT_RAPID_DECEL,      /* Rapid deceleration */
    ES_ALGO_EVENT_SHARP_TURN_LEFT,  /* Sharp left turn */
    ES_ALGO_EVENT_SHARP_TURN_RIGHT, /* Sharp right turn */
    ES_ALGO_EVENT_TIPPING,          /* Vehicle tipping */
    ES_ALGO_EVENT_RECOVERED,        /* Recovered from tipping */
} es_algo_event_type_t;

/**
 * @brief 3D vector structure (using fixed-point for calculations)
 */
typedef struct {
    int32_t x;
    int32_t y;
    int32_t z;
} es_algo_vector3_t;

/**
 * @brief Filtered sensor data
 */
typedef struct {
    es_algo_vector3_t accel_mg;     /* Filtered acceleration (mg, fixed-point) */
    es_algo_vector3_t gyro_mdps;    /* Filtered gyroscope (mdps, fixed-point) */
    es_algo_vector3_t gravity_mg;   /* Estimated gravity vector (mg, fixed-point) */
    int16_t temperature_c;          /* Temperature (°C * 10) */
    uint32_t timestamp;             /* Timestamp (ms) */
    bool valid;                     /* Data validity flag */
} es_algo_sensor_data_t;

/**
 * @brief Processed GPS data
 */
typedef struct {
    int32_t latitude_deg_e7;        /* Latitude (degrees * 1e7) */
    int32_t longitude_deg_e7;       /* Longitude (degrees * 1e7) */
    uint16_t speed_kmh;             /* Speed (km/h) */
    uint16_t heading_deg;           /* Heading (degrees) */
    int16_t acceleration_ms2_x10;   /* GPS-derived acceleration (m/s² * 10) */
    uint8_t satellites;             /* Number of satellites */
    uint16_t hdop;                  /* Horizontal dilution of precision (x100) */
    uint32_t timestamp;             /* Timestamp (ms) */
    bool valid;                     /* Data validity flag */
} es_algo_gps_data_t;

/**
 * @brief Vehicle orientation estimation
 */
typedef struct {
    es_algo_vector3_t forward;      /* Vehicle forward direction (normalized, fixed-point) */
    es_algo_vector3_t right;        /* Vehicle right direction (normalized, fixed-point) */
    es_algo_vector3_t up;           /* Vehicle up direction (normalized, fixed-point) */
    int16_t roll_deg_x100;          /* Roll angle (degrees * 100) */
    int16_t pitch_deg_x100;         /* Pitch angle (degrees * 100) */
    int16_t yaw_deg_x100;           /* Yaw angle (degrees * 100) */
    uint16_t gps_heading_deg;       /* GPS heading (degrees) */
    bool calibrated;                /* Calibration status */
} es_algo_orientation_t;

/**
 * @brief Vehicle event data
 */
typedef struct {
    es_algo_event_type_t type;      /* Event type */
    int16_t magnitude_x100;         /* Event magnitude (fixed-point * 100) */
    uint32_t timestamp;             /* Event timestamp (ms) */
    es_algo_sensor_data_t sensor;   /* Sensor data at event */
    es_algo_gps_data_t gps;         /* GPS data at event */
} es_algo_event_t;

/**
 * @brief Data filter state
 */
typedef struct {
    es_algo_vector3_t accel_buffer[ES_ALGO_FILTER_WINDOW_SIZE];
    es_algo_vector3_t gyro_buffer[ES_ALGO_FILTER_WINDOW_SIZE];
    uint8_t buffer_index;
    uint8_t buffer_count;
    es_algo_vector3_t accel_sum;
    es_algo_vector3_t gyro_sum;
} es_algo_filter_state_t;

/**
 * @brief Algorithm context structure
 */
typedef struct {
    /* Data filtering */
    es_algo_filter_state_t filter;
    
    /* Sensor and GPS data */
    es_algo_sensor_data_t current_sensor;
    es_algo_gps_data_t current_gps;
    es_algo_gps_data_t previous_gps;
    
    /* Vehicle orientation */
    es_algo_orientation_t orientation;
    uint16_t calibration_count;
    es_algo_vector3_t calibration_sum;
    
    /* Event detection state */
    es_algo_event_type_t last_event;
    uint32_t last_event_time;
    uint8_t event_debounce_count;
    
    /* Statistics */
    uint32_t sensor_sample_count;
    uint32_t gps_sample_count;
    uint32_t invalid_sensor_count;
    uint32_t invalid_gps_count;
    
    /* Flags */
    bool initialized;
    bool calibration_complete;
} es_algo_context_t;

/* Event type for scheduler event system */
#define EVENT_TYPE_VEHICLE_EVENT    0x1001

/**
 * @brief Initialize algorithm module
 * @return 0 on success, negative on error
 */
int es_algo_init(void);

/**
 * @brief Deinitialize algorithm module
 */
void es_algo_deinit(void);

/**
 * @brief Process new sensor data
 * @param accel Raw accelerometer data (mg, after sensitivity applied)
 * @param gyro Raw gyroscope data (mdps, after sensitivity applied)
 * @param temperature Temperature data (°C)
 * @return 0 on success, negative on error
 */
int es_algo_process_sensor_data(const stc_lsm6dsl_axis_t *accel, 
                               const stc_lsm6dsl_axis_t *gyro, 
                               float temperature);

/**
 * @brief Process new GPS data
 * @param gps GPS data structure
 * @return 0 on success, negative on error
 */
int es_algo_process_gps_data(const gps_t *gps);

/**
 * @brief Get current algorithm context (for debugging)
 * @return Pointer to algorithm context
 */
const es_algo_context_t *es_algo_get_context(void);

/**
 * @brief Get algorithm statistics
 * @param sensor_samples Output: number of sensor samples processed
 * @param gps_samples Output: number of GPS samples processed
 * @param invalid_sensor Output: number of invalid sensor samples
 * @param invalid_gps Output: number of invalid GPS samples
 */
void es_algo_get_statistics(uint32_t *sensor_samples, uint32_t *gps_samples,
                           uint32_t *invalid_sensor, uint32_t *invalid_gps);

/**
 * @brief Reset algorithm state (for testing)
 */
void es_algo_reset(void);

/**
 * @brief Initialize algorithm tasks
 * @return 0 on success, negative on error
 */
int es_algo_task_init(void);

/**
 * @brief Deinitialize algorithm tasks
 */
void es_algo_task_deinit(void);

/**
 * @brief Initialize algorithm tasks
 * @return 0 on success, negative on error
 */
int es_algo_task_init(void);

/**
 * @brief Deinitialize algorithm tasks
 */
void es_algo_task_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_ALGO_H__ */
