/**
 * @file es_algo.c
 * @brief Vehicle Event Detection Algorithm Module Implementation
 * <AUTHOR> Framework
 * @date 2025-07-28
 * @version 1.0
 */

#include "es_algo.h"
#include "es_log.h"
#include "es_drv_os.h"
#include <string.h>
#include <math.h>

#define TAG "ALGO"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* Static algorithm context */
static es_algo_context_t s_algo_ctx;

/* Static function declarations */
static bool es_algo_validate_sensor_data(const stc_lsm6dsl_axis_t *accel, const stc_lsm6dsl_axis_t *gyro);
static bool es_algo_validate_gps_data(const gps_t *gps);
static void es_algo_filter_sensor_data(const stc_lsm6dsl_axis_t *accel, const stc_lsm6dsl_axis_t *gyro);
static void es_algo_update_orientation(void);
static void es_algo_detect_events(void);
static void es_algo_publish_event(es_algo_event_type_t event_type, float magnitude);
static float es_algo_vector_magnitude(const es_algo_vector3_t *vec);
static es_algo_vector3_t es_algo_vector_normalize(const es_algo_vector3_t *vec);
static float es_algo_vector_dot(const es_algo_vector3_t *a, const es_algo_vector3_t *b);

/**
 * @brief Initialize algorithm module
 */
int es_algo_init(void)
{
    if (s_algo_ctx.initialized) {
        return 0;
    }

    /* Clear context */
    memset(&s_algo_ctx, 0, sizeof(s_algo_ctx));

    /* Initialize filter state */
    s_algo_ctx.filter.buffer_index = 0;
    s_algo_ctx.filter.buffer_count = 0;
    memset(&s_algo_ctx.filter.accel_sum, 0, sizeof(es_algo_vector3_t));
    memset(&s_algo_ctx.filter.gyro_sum, 0, sizeof(es_algo_vector3_t));

    /* Initialize orientation with default values */
    s_algo_ctx.orientation.forward = (es_algo_vector3_t){1.0f, 0.0f, 0.0f};
    s_algo_ctx.orientation.right = (es_algo_vector3_t){0.0f, 1.0f, 0.0f};
    s_algo_ctx.orientation.up = (es_algo_vector3_t){0.0f, 0.0f, 1.0f};
    s_algo_ctx.orientation.calibrated = false;

    /* Set gravity estimate to 1g in Z direction (default assumption) */
    s_algo_ctx.current_sensor.gravity_mg = (es_algo_vector3_t){0.0f, 0.0f, 1000.0f};

    s_algo_ctx.initialized = true;

    ES_PRINTF_I(TAG, "Algorithm module initialized");
    return 0;
}

/**
 * @brief Deinitialize algorithm module
 */
void es_algo_deinit(void)
{
    if (!s_algo_ctx.initialized) {
        return;
    }

    memset(&s_algo_ctx, 0, sizeof(s_algo_ctx));
    ES_PRINTF_I(TAG, "Algorithm module deinitialized");
}

/**
 * @brief Process new sensor data
 */
int es_algo_process_sensor_data(const stc_lsm6dsl_axis_t *accel, 
                               const stc_lsm6dsl_axis_t *gyro, 
                               float temperature)
{
    if (!s_algo_ctx.initialized || !accel || !gyro) {
        return -1;
    }

    s_algo_ctx.sensor_sample_count++;

    /* Validate sensor data */
    if (!es_algo_validate_sensor_data(accel, gyro)) {
        s_algo_ctx.invalid_sensor_count++;
        ES_PRINTF_I(TAG, "Invalid sensor data: accel(%d,%d,%d) gyro(%d,%d,%d)", 
                   accel->x, accel->y, accel->z, gyro->x, gyro->y, gyro->z);
        return -2;
    }

    /* Apply filtering */
    es_algo_filter_sensor_data(accel, gyro);

    /* Update sensor data */
    s_algo_ctx.current_sensor.temperature = temperature;
    s_algo_ctx.current_sensor.timestamp = es_drv_os_get_tick_ms();
    s_algo_ctx.current_sensor.valid = true;

    /* Update vehicle orientation estimation */
    es_algo_update_orientation();

    /* Detect events */
    es_algo_detect_events();

    return 0;
}

/**
 * @brief Process new GPS data
 */
int es_algo_process_gps_data(const gps_t *gps)
{
    if (!s_algo_ctx.initialized || !gps) {
        return -1;
    }

    s_algo_ctx.gps_sample_count++;

    /* Validate GPS data */
    if (!es_algo_validate_gps_data(gps)) {
        s_algo_ctx.invalid_gps_count++;
        ES_PRINTF_I(TAG, "Invalid GPS data: sat=%d, hdop=%d, speed=%d", 
                   gps->satellites, gps->hdop, gps->speed);
        return -2;
    }

    /* Store previous GPS data */
    s_algo_ctx.previous_gps = s_algo_ctx.current_gps;

    /* Update current GPS data */
    s_algo_ctx.current_gps.latitude = (float)gps->lat / 1000000.0f;
    s_algo_ctx.current_gps.longitude = (float)gps->lon / 1000000.0f;
    s_algo_ctx.current_gps.speed_kmh = gps->speed;
    s_algo_ctx.current_gps.heading_deg = gps->direction;
    s_algo_ctx.current_gps.satellites = gps->satellites;
    s_algo_ctx.current_gps.hdop = gps->hdop;
    s_algo_ctx.current_gps.timestamp = es_drv_os_get_tick_ms();
    s_algo_ctx.current_gps.valid = true;

    /* Calculate GPS-derived acceleration if we have previous data */
    if (s_algo_ctx.previous_gps.valid && s_algo_ctx.previous_gps.timestamp > 0) {
        uint32_t dt_ms = s_algo_ctx.current_gps.timestamp - s_algo_ctx.previous_gps.timestamp;
        if (dt_ms > 0 && dt_ms < 5000) { /* Valid time difference (< 5 seconds) */
            float dt_s = (float)dt_ms / 1000.0f;
            float speed_diff_ms = ((float)s_algo_ctx.current_gps.speed_kmh - 
                                  (float)s_algo_ctx.previous_gps.speed_kmh) / 3.6f;
            s_algo_ctx.current_gps.acceleration_ms2 = speed_diff_ms / dt_s;
        }
    }

    ES_PRINTF_I(TAG, "GPS: lat=%.6f, lon=%.6f, speed=%d km/h, heading=%d°, sat=%d", 
               s_algo_ctx.current_gps.latitude, s_algo_ctx.current_gps.longitude,
               s_algo_ctx.current_gps.speed_kmh, s_algo_ctx.current_gps.heading_deg,
               s_algo_ctx.current_gps.satellites);

    return 0;
}

/**
 * @brief Get current algorithm context
 */
const es_algo_context_t *es_algo_get_context(void)
{
    return &s_algo_ctx;
}

/**
 * @brief Get algorithm statistics
 */
void es_algo_get_statistics(uint32_t *sensor_samples, uint32_t *gps_samples,
                           uint32_t *invalid_sensor, uint32_t *invalid_gps)
{
    if (sensor_samples) *sensor_samples = s_algo_ctx.sensor_sample_count;
    if (gps_samples) *gps_samples = s_algo_ctx.gps_sample_count;
    if (invalid_sensor) *invalid_sensor = s_algo_ctx.invalid_sensor_count;
    if (invalid_gps) *invalid_gps = s_algo_ctx.invalid_gps_count;
}

/**
 * @brief Reset algorithm state
 */
void es_algo_reset(void)
{
    if (!s_algo_ctx.initialized) {
        return;
    }

    /* Reset statistics */
    s_algo_ctx.sensor_sample_count = 0;
    s_algo_ctx.gps_sample_count = 0;
    s_algo_ctx.invalid_sensor_count = 0;
    s_algo_ctx.invalid_gps_count = 0;

    /* Reset calibration */
    s_algo_ctx.calibration_count = 0;
    s_algo_ctx.calibration_complete = false;
    s_algo_ctx.orientation.calibrated = false;

    /* Reset event state */
    s_algo_ctx.last_event = ES_ALGO_EVENT_NONE;
    s_algo_ctx.last_event_time = 0;
    s_algo_ctx.event_debounce_count = 0;

    ES_PRINTF_I(TAG, "Algorithm state reset");
}

/**
 * @brief Validate sensor data
 */
static bool es_algo_validate_sensor_data(const stc_lsm6dsl_axis_t *accel, const stc_lsm6dsl_axis_t *gyro)
{
    /* Check accelerometer data range (mg) */
    if (abs(accel->x) > ES_ALGO_MAX_ACCEL_MG || 
        abs(accel->y) > ES_ALGO_MAX_ACCEL_MG || 
        abs(accel->z) > ES_ALGO_MAX_ACCEL_MG) {
        return false;
    }

    /* Check gyroscope data range (mdps) */
    if (abs(gyro->x) > ES_ALGO_MAX_GYRO_MDPS || 
        abs(gyro->y) > ES_ALGO_MAX_GYRO_MDPS || 
        abs(gyro->z) > ES_ALGO_MAX_GYRO_MDPS) {
        return false;
    }

    /* Check for reasonable gravity magnitude (should be around 1000mg ± 500mg) */
    float accel_mag = sqrtf((float)accel->x * accel->x + 
                           (float)accel->y * accel->y + 
                           (float)accel->z * accel->z);
    if (accel_mag < 500.0f || accel_mag > 1500.0f) {
        return false;
    }

    return true;
}

/**
 * @brief Validate GPS data
 */
static bool es_algo_validate_gps_data(const gps_t *gps)
{
    /* Check minimum satellite count */
    if (gps->satellites < ES_ALGO_GPS_MIN_SATELLITES) {
        return false;
    }

    /* Check HDOP (horizontal dilution of precision) */
    if (gps->hdop > ES_ALGO_GPS_MAX_HDOP) {
        return false;
    }

    /* Check reasonable speed range (0-300 km/h) */
    if (gps->speed > 300) {
        return false;
    }

    /* Check reasonable direction range (0-359 degrees) */
    if (gps->direction >= 360) {
        return false;
    }

    return true;
}

/**
 * @brief Apply moving average filter to sensor data
 */
static void es_algo_filter_sensor_data(const stc_lsm6dsl_axis_t *accel, const stc_lsm6dsl_axis_t *gyro)
{
    es_algo_filter_state_t *filter = &s_algo_ctx.filter;

    /* Convert raw data to float */
    es_algo_vector3_t new_accel = {(float)accel->x, (float)accel->y, (float)accel->z};
    es_algo_vector3_t new_gyro = {(float)gyro->x, (float)gyro->y, (float)gyro->z};

    /* Remove old data from sum if buffer is full */
    if (filter->buffer_count >= ES_ALGO_FILTER_WINDOW_SIZE) {
        filter->accel_sum.x -= filter->accel_buffer[filter->buffer_index].x;
        filter->accel_sum.y -= filter->accel_buffer[filter->buffer_index].y;
        filter->accel_sum.z -= filter->accel_buffer[filter->buffer_index].z;

        filter->gyro_sum.x -= filter->gyro_buffer[filter->buffer_index].x;
        filter->gyro_sum.y -= filter->gyro_buffer[filter->buffer_index].y;
        filter->gyro_sum.z -= filter->gyro_buffer[filter->buffer_index].z;
    } else {
        filter->buffer_count++;
    }

    /* Add new data to buffer and sum */
    filter->accel_buffer[filter->buffer_index] = new_accel;
    filter->gyro_buffer[filter->buffer_index] = new_gyro;

    filter->accel_sum.x += new_accel.x;
    filter->accel_sum.y += new_accel.y;
    filter->accel_sum.z += new_accel.z;

    filter->gyro_sum.x += new_gyro.x;
    filter->gyro_sum.y += new_gyro.y;
    filter->gyro_sum.z += new_gyro.z;

    /* Calculate filtered values */
    s_algo_ctx.current_sensor.accel_mg.x = filter->accel_sum.x / filter->buffer_count;
    s_algo_ctx.current_sensor.accel_mg.y = filter->accel_sum.y / filter->buffer_count;
    s_algo_ctx.current_sensor.accel_mg.z = filter->accel_sum.z / filter->buffer_count;

    s_algo_ctx.current_sensor.gyro_mdps.x = filter->gyro_sum.x / filter->buffer_count;
    s_algo_ctx.current_sensor.gyro_mdps.y = filter->gyro_sum.y / filter->buffer_count;
    s_algo_ctx.current_sensor.gyro_mdps.z = filter->gyro_sum.z / filter->buffer_count;

    /* Update buffer index */
    filter->buffer_index = (filter->buffer_index + 1) % ES_ALGO_FILTER_WINDOW_SIZE;
}

/**
 * @brief Update vehicle orientation estimation
 */
static void es_algo_update_orientation(void)
{
    /* Simple gravity-based orientation estimation */
    es_algo_vector3_t accel_normalized = es_algo_vector_normalize(&s_algo_ctx.current_sensor.accel_mg);

    /* Update gravity estimate (low-pass filter) */
    const float alpha = 0.1f; /* Filter coefficient */
    s_algo_ctx.current_sensor.gravity_mg.x = (1.0f - alpha) * s_algo_ctx.current_sensor.gravity_mg.x + alpha * s_algo_ctx.current_sensor.accel_mg.x;
    s_algo_ctx.current_sensor.gravity_mg.y = (1.0f - alpha) * s_algo_ctx.current_sensor.gravity_mg.y + alpha * s_algo_ctx.current_sensor.accel_mg.y;
    s_algo_ctx.current_sensor.gravity_mg.z = (1.0f - alpha) * s_algo_ctx.current_sensor.gravity_mg.z + alpha * s_algo_ctx.current_sensor.accel_mg.z;

    /* Calculate roll and pitch from gravity vector */
    es_algo_vector3_t gravity_normalized = es_algo_vector_normalize(&s_algo_ctx.current_sensor.gravity_mg);

    s_algo_ctx.orientation.roll_deg = atan2f(gravity_normalized.y, gravity_normalized.z) * 180.0f / M_PI;
    s_algo_ctx.orientation.pitch_deg = atan2f(-gravity_normalized.x, sqrtf(gravity_normalized.y * gravity_normalized.y + gravity_normalized.z * gravity_normalized.z)) * 180.0f / M_PI;

    /* Update orientation vectors */
    s_algo_ctx.orientation.up = gravity_normalized;

    /* If we have GPS heading, use it to determine forward direction */
    if (s_algo_ctx.current_gps.valid && s_algo_ctx.current_gps.speed_kmh > ES_ALGO_SPEED_THRESHOLD_KMH) {
        float heading_rad = (float)s_algo_ctx.current_gps.heading_deg * M_PI / 180.0f;
        s_algo_ctx.orientation.forward.x = cosf(heading_rad);
        s_algo_ctx.orientation.forward.y = sinf(heading_rad);
        s_algo_ctx.orientation.forward.z = 0.0f; /* Assume horizontal movement */

        /* Calculate right vector as cross product of up and forward */
        s_algo_ctx.orientation.right.x = s_algo_ctx.orientation.up.y * s_algo_ctx.orientation.forward.z - s_algo_ctx.orientation.up.z * s_algo_ctx.orientation.forward.y;
        s_algo_ctx.orientation.right.y = s_algo_ctx.orientation.up.z * s_algo_ctx.orientation.forward.x - s_algo_ctx.orientation.up.x * s_algo_ctx.orientation.forward.z;
        s_algo_ctx.orientation.right.z = s_algo_ctx.orientation.up.x * s_algo_ctx.orientation.forward.y - s_algo_ctx.orientation.up.y * s_algo_ctx.orientation.forward.x;

        s_algo_ctx.orientation.calibrated = true;
        s_algo_ctx.orientation.yaw_deg = (float)s_algo_ctx.current_gps.heading_deg;
    }
}

/**
 * @brief Detect vehicle events
 */
static void es_algo_detect_events(void)
{
    if (!s_algo_ctx.current_sensor.valid) {
        return;
    }

    uint32_t current_time = es_drv_os_get_tick_ms();

    /* Skip event detection if too soon after last event (debounce) */
    if (current_time - s_algo_ctx.last_event_time < 1000) { /* 1 second debounce */
        return;
    }

    /* Check for tipping event first (highest priority) */
    float tilt_angle = sqrtf(s_algo_ctx.orientation.roll_deg * s_algo_ctx.orientation.roll_deg +
                            s_algo_ctx.orientation.pitch_deg * s_algo_ctx.orientation.pitch_deg);

    if (tilt_angle > ES_ALGO_TILT_THRESHOLD_DEG) {
        if (s_algo_ctx.last_event != ES_ALGO_EVENT_TIPPING) {
            es_algo_publish_event(ES_ALGO_EVENT_TIPPING, tilt_angle);
        }
        return;
    } else if (s_algo_ctx.last_event == ES_ALGO_EVENT_TIPPING && tilt_angle < ES_ALGO_TILT_THRESHOLD_DEG - 10.0f) {
        /* Recovery from tipping (with hysteresis) */
        es_algo_publish_event(ES_ALGO_EVENT_RECOVERED, tilt_angle);
        return;
    }

    /* Only detect motion events if vehicle is moving */
    if (!s_algo_ctx.current_gps.valid || s_algo_ctx.current_gps.speed_kmh < ES_ALGO_SPEED_THRESHOLD_KMH) {
        return;
    }

    /* Project acceleration onto vehicle coordinate system if calibrated */
    es_algo_vector3_t accel_vehicle = s_algo_ctx.current_sensor.accel_mg;
    if (s_algo_ctx.orientation.calibrated) {
        /* Transform acceleration to vehicle coordinates */
        float forward_accel = es_algo_vector_dot(&s_algo_ctx.current_sensor.accel_mg, &s_algo_ctx.orientation.forward);
        float right_accel = es_algo_vector_dot(&s_algo_ctx.current_sensor.accel_mg, &s_algo_ctx.orientation.right);

        accel_vehicle.x = forward_accel;
        accel_vehicle.y = right_accel;
    }

    /* Check for rapid acceleration/deceleration */
    if (accel_vehicle.x > ES_ALGO_ACCEL_THRESHOLD_MG) {
        es_algo_publish_event(ES_ALGO_EVENT_RAPID_ACCEL, accel_vehicle.x);
    } else if (accel_vehicle.x < -ES_ALGO_DECEL_THRESHOLD_MG) {
        es_algo_publish_event(ES_ALGO_EVENT_RAPID_DECEL, -accel_vehicle.x);
    }

    /* Check for sharp turns using gyroscope */
    float yaw_rate = ES_ALGO_MDPS_TO_DPS(s_algo_ctx.current_sensor.gyro_mdps.z);
    if (yaw_rate > ES_ALGO_MDPS_TO_DPS(ES_ALGO_TURN_THRESHOLD_MDPS)) {
        es_algo_publish_event(ES_ALGO_EVENT_SHARP_TURN_LEFT, yaw_rate);
    } else if (yaw_rate < -ES_ALGO_MDPS_TO_DPS(ES_ALGO_TURN_THRESHOLD_MDPS)) {
        es_algo_publish_event(ES_ALGO_EVENT_SHARP_TURN_RIGHT, -yaw_rate);
    }
}

/**
 * @brief Publish vehicle event
 */
static void es_algo_publish_event(es_algo_event_type_t event_type, float magnitude)
{
    es_algo_event_t event;

    /* Fill event data */
    event.type = event_type;
    event.magnitude = magnitude;
    event.timestamp = es_drv_os_get_tick_ms();
    event.sensor = s_algo_ctx.current_sensor;
    event.gps = s_algo_ctx.current_gps;

    /* Update last event info */
    s_algo_ctx.last_event = event_type;
    s_algo_ctx.last_event_time = event.timestamp;

    /* Publish event to scheduler */
    es_scheduler_event_publish(es_scheduler_get_default(), EVENT_TYPE_VEHICLE_EVENT, &event, sizeof(event));

    /* Log event */
    const char *event_names[] = {
        "NONE", "RAPID_ACCEL", "RAPID_DECEL", "SHARP_TURN_LEFT", "SHARP_TURN_RIGHT", "TIPPING", "RECOVERED"
    };

    if (event_type < sizeof(event_names) / sizeof(event_names[0])) {
        ES_PRINTF_I(TAG, "Event: %s, magnitude=%.2f", event_names[event_type], magnitude);
    }
}

/**
 * @brief Calculate vector magnitude
 */
static float es_algo_vector_magnitude(const es_algo_vector3_t *vec)
{
    return sqrtf(vec->x * vec->x + vec->y * vec->y + vec->z * vec->z);
}

/**
 * @brief Normalize vector
 */
static es_algo_vector3_t es_algo_vector_normalize(const es_algo_vector3_t *vec)
{
    float mag = es_algo_vector_magnitude(vec);
    es_algo_vector3_t result = {0.0f, 0.0f, 0.0f};

    if (mag > 0.000001f) {
        result.x = vec->x / mag;
        result.y = vec->y / mag;
        result.z = vec->z / mag;
    }

    return result;
}

/**
 * @brief Calculate dot product of two vectors
 */
static float es_algo_vector_dot(const es_algo_vector3_t *a, const es_algo_vector3_t *b)
{
    return a->x * b->x + a->y * b->y + a->z * b->z;
}
