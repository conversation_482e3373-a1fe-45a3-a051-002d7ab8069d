/**
 * @file es_algo_demo.c
 * @brief Vehicle Algorithm Demo and Integration Example
 * <AUTHOR> Framework
 * @date 2025-07-28
 * @version 1.0
 */

#include "es_algo.h"
#include "es_log.h"
#include "es_scheduler.h"
#include "es_drv_os.h"

#define TAG "ALGO_DEMO"

/* Event subscriber for vehicle events */
static es_event_subscriber_t vehicle_event_subscriber;

/**
 * @brief Vehicle event handler
 */
static void vehicle_event_handler(const es_event_t *event, void *ctx)
{
    if (event->type == EVENT_TYPE_VEHICLE_EVENT && event->data_len == sizeof(es_algo_event_t)) {
        const es_algo_event_t *vehicle_event = (const es_algo_event_t *)event->u.data;
        
        const char *event_names[] = {
            "NONE", "RAPID_ACCEL", "RAPID_DECEL", "SHARP_TURN_LEFT", 
            "SHARP_TURN_RIGHT", "TIPPING", "RECOVERED"
        };
        
        if (vehicle_event->type < sizeof(event_names) / sizeof(event_names[0])) {
            ES_PRINTF_I(TAG, "Vehicle Event: %s, magnitude=%.2f, timestamp=%lu", 
                       event_names[vehicle_event->type], 
                       vehicle_event->magnitude,
                       vehicle_event->timestamp);
            
            /* Log sensor data at event time */
            ES_PRINTF_I(TAG, "  Accel: %.1f,%.1f,%.1f mg", 
                       vehicle_event->sensor.accel_mg.x,
                       vehicle_event->sensor.accel_mg.y,
                       vehicle_event->sensor.accel_mg.z);
            
            ES_PRINTF_I(TAG, "  Gyro: %.1f,%.1f,%.1f mdps", 
                       vehicle_event->sensor.gyro_mdps.x,
                       vehicle_event->sensor.gyro_mdps.y,
                       vehicle_event->sensor.gyro_mdps.z);
            
            if (vehicle_event->gps.valid) {
                ES_PRINTF_I(TAG, "  GPS: speed=%d km/h, heading=%d°", 
                           vehicle_event->gps.speed_kmh,
                           vehicle_event->gps.heading_deg);
            }
        }
    }
}

/**
 * @brief Initialize algorithm demo
 */
int es_algo_demo_init(void)
{
    /* Subscribe to vehicle events */
    es_scheduler_event_subscriber_init(&vehicle_event_subscriber, 
                                       EVENT_TYPE_VEHICLE_EVENT,
                                       vehicle_event_handler, 
                                       NULL);
    
    int ret = es_scheduler_event_subscribe(es_scheduler_get_default(), &vehicle_event_subscriber);
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to subscribe to vehicle events: %d", ret);
        return ret;
    }
    
    /* Initialize algorithm tasks */
    ret = es_algo_task_init();
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize algorithm tasks: %d", ret);
        return ret;
    }
    
    ES_PRINTF_I(TAG, "Algorithm demo initialized successfully");
    ES_PRINTF_I(TAG, "Vehicle events will be logged when detected");
    
    return 0;
}

/**
 * @brief Deinitialize algorithm demo
 */
void es_algo_demo_deinit(void)
{
    /* Unsubscribe from events */
    es_scheduler_event_unsubscribe(es_scheduler_get_default(), &vehicle_event_subscriber);
    
    /* Deinitialize algorithm tasks */
    es_algo_task_deinit();
    
    ES_PRINTF_I(TAG, "Algorithm demo deinitialized");
}

/**
 * @brief Test algorithm with simulated data
 */
void es_algo_test_simulation(void)
{
    ES_PRINTF_I(TAG, "Starting algorithm simulation test...");
    
    /* Initialize algorithm module */
    int ret = es_algo_init();
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize algorithm: %d", ret);
        return;
    }
    
    /* Simulate normal driving data */
    stc_lsm6dsl_axis_t normal_accel = {0, 0, 1000}; /* 1g in Z direction */
    stc_lsm6dsl_axis_t normal_gyro = {0, 0, 0};     /* No rotation */
    
    ES_PRINTF_I(TAG, "Simulating normal driving...");
    for (int i = 0; i < 10; i++) {
        es_algo_process_sensor_data(&normal_accel, &normal_gyro, 25.0f);
        es_drv_os_delay_ms(20); /* 50Hz simulation */
    }
    
    /* Simulate rapid acceleration */
    stc_lsm6dsl_axis_t accel_event = {500, 0, 1000}; /* 0.5g forward acceleration */
    ES_PRINTF_I(TAG, "Simulating rapid acceleration...");
    es_algo_process_sensor_data(&accel_event, &normal_gyro, 25.0f);
    
    /* Simulate sharp turn */
    stc_lsm6dsl_axis_t turn_gyro = {0, 0, 40000}; /* 40 deg/s yaw rate */
    ES_PRINTF_I(TAG, "Simulating sharp turn...");
    es_algo_process_sensor_data(&normal_accel, &turn_gyro, 25.0f);
    
    /* Simulate tipping */
    stc_lsm6dsl_axis_t tip_accel = {700, 700, 700}; /* 45 degree tilt */
    ES_PRINTF_I(TAG, "Simulating vehicle tipping...");
    es_algo_process_sensor_data(&tip_accel, &normal_gyro, 25.0f);
    
    /* Simulate GPS data */
    gps_t gps_data = {
        .time = es_drv_os_get_tick_ms(),
        .src = 0,
        .valid = 0,
        .f_lng = 0,
        .f_lat = 1,
        .satellites = 8,
        .height = 100,
        .lon = 116400000,  /* 116.4° E (Beijing) */
        .lat = 39900000,   /* 39.9° N (Beijing) */
        .speed = 60,       /* 60 km/h */
        .direction = 90,   /* East */
        .pdod = 100,
        .hdop = 100,
        .vdop = 100,
        .ign_status = 1,
        .hacc = 300,
        .is_turn_point = 0,
        .signal_strength = 80
    };
    
    ES_PRINTF_I(TAG, "Simulating GPS data...");
    es_algo_process_gps_data(&gps_data);
    
    /* Get statistics */
    uint32_t sensor_samples, gps_samples, invalid_sensor, invalid_gps;
    es_algo_get_statistics(&sensor_samples, &gps_samples, &invalid_sensor, &invalid_gps);
    
    ES_PRINTF_I(TAG, "Test completed. Stats: sensor=%lu, gps=%lu, invalid_sensor=%lu, invalid_gps=%lu",
               sensor_samples, gps_samples, invalid_sensor, invalid_gps);
    
    /* Get algorithm context */
    const es_algo_context_t *ctx = es_algo_get_context();
    ES_PRINTF_I(TAG, "Orientation calibrated: %s", ctx->orientation.calibrated ? "Yes" : "No");
    
    ES_PRINTF_I(TAG, "Algorithm simulation test completed");
}

/**
 * @brief Print algorithm usage instructions
 */
void es_algo_print_usage(void)
{
    ES_PRINTF_I(TAG, "=== ES Algorithm Module Usage ===");
    ES_PRINTF_I(TAG, "1. Call es_algo_demo_init() to start vehicle event detection");
    ES_PRINTF_I(TAG, "2. The module will automatically:");
    ES_PRINTF_I(TAG, "   - Read LSM6DSL sensor data at 50Hz");
    ES_PRINTF_I(TAG, "   - Read GPS data at 1Hz");
    ES_PRINTF_I(TAG, "   - Filter and validate data");
    ES_PRINTF_I(TAG, "   - Detect vehicle orientation using GPS heading");
    ES_PRINTF_I(TAG, "   - Detect events: rapid accel/decel, sharp turns, tipping");
    ES_PRINTF_I(TAG, "   - Publish events to scheduler event system");
    ES_PRINTF_I(TAG, "3. Events are logged with magnitude and sensor/GPS data");
    ES_PRINTF_I(TAG, "4. Call es_algo_test_simulation() for testing with simulated data");
    ES_PRINTF_I(TAG, "5. Call es_algo_demo_deinit() to stop detection");
    ES_PRINTF_I(TAG, "");
    ES_PRINTF_I(TAG, "Event Thresholds:");
    ES_PRINTF_I(TAG, "- Rapid acceleration: >%d mg", ES_ALGO_ACCEL_THRESHOLD_MG);
    ES_PRINTF_I(TAG, "- Rapid deceleration: >%d mg", ES_ALGO_DECEL_THRESHOLD_MG);
    ES_PRINTF_I(TAG, "- Sharp turn: >%d mdps", ES_ALGO_TURN_THRESHOLD_MDPS);
    ES_PRINTF_I(TAG, "- Vehicle tipping: >%.1f degrees", ES_ALGO_TILT_THRESHOLD_DEG);
    ES_PRINTF_I(TAG, "- Minimum speed for motion events: %d km/h", ES_ALGO_SPEED_THRESHOLD_KMH);
    ES_PRINTF_I(TAG, "================================");
}
